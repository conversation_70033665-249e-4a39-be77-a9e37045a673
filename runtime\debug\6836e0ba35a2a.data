a:14:{s:6:"config";s:4971:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:10:"2.0.52-dev";s:11:"application";a:8:{s:3:"yii";s:10:"2.0.52-dev";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:16:{s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/base";s:67:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:62:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:60:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-grid/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:77:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-jui/src";}}s:33:"2amigos/yii2-arrayquery-component";a:3:{s:4:"name";s:33:"2amigos/yii2-arrayquery-component";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@dosamigos/arrayquery";s:75:"D:\OSPanel\domains\silverzavod\vendor/2amigos/yii2-arrayquery-component/src";}}s:17:"yii2mod/yii2-rbac";a:3:{s:4:"name";s:17:"yii2mod/yii2-rbac";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii2mod/rbac";s:55:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-rbac";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.25.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:10:"@yii/faker";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.6.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:23:"3.0.9999999.9999999-dev";s:5:"alias";a:10:{s:10:"@yii/queue";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:71:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:72:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/file";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:76:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:78:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:81:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:68:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.11.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap4/src";}}s:20:"yii2mod/yii2-swagger";a:3:{s:4:"name";s:20:"yii2mod/yii2-swagger";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:16:"@yii2mod/swagger";s:58:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-swagger";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:46722:"a:1:{s:8:"messages";a:42:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.1124;i:4;a:0:{}i:5;i:2612008;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.118474;i:4;a:0:{}i:5;i:2729016;}i:2;a:6:{i:0;s:53:"Bootstrap with app\components\SessionTimeoutComponent";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.120915;i:4;a:0:{}i:5;i:2770224;}i:3;a:6:{i:0;s:22:"Bootstrap with Closure";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.120998;i:4;a:0:{}i:5;i:2770600;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.174323;i:4;a:0:{}i:5;i:3915800;}i:5;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.203794;i:4;a:0:{}i:5;i:4726056;}i:6;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.218054;i:4;a:0:{}i:5;i:5115720;}i:7;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.234525;i:4;a:0:{}i:5;i:5580128;}i:8;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.236572;i:4;a:0:{}i:5;i:5607520;}i:20;a:6:{i:0;s:57:"Route requested: 'api/manufacter/send-to-material-defect'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.244907;i:4;a:0:{}i:5;i:5781192;}i:21;a:6:{i:0;s:19:"Loading module: api";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.244922;i:4;a:0:{}i:5;i:5782840;}i:22;a:6:{i:0;s:52:"Route to run: api/manufacter/send-to-material-defect";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.256114;i:4;a:0:{}i:5;i:6143280;}i:23;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.302987;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7484176;}i:26;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.409512;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8015576;}i:29;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.451182;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8061488;}i:32;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.464164;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8357296;}i:35;a:6:{i:0;s:55:"User '7' logged in from 127.0.0.1. Session not enabled.";i:1;i:4;i:2;s:19:"yii\web\User::login";i:3;d:**********.47451;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8643944;}i:36;a:6:{i:0;s:65:"Rate limit skipped: "user" does not implement RateLimitInterface.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:**********.474616;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8644536;}i:37;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.479665;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8832120;}i:40;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.485205;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8838744;}i:43;a:6:{i:0;s:27:"Checking role: manufacturer";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:**********.488461;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8842136;}i:44;a:6:{i:0;s:94:"Running action: app\modules\api\controllers\ManufacterController::actionSendToMaterialDefect()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.488501;i:4;a:0:{}i:5;i:8841296;}i:45;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.500548;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:30;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9115344;}i:48;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.509947;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:30;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9126136;}i:51;a:6:{i:0;s:37:"SELECT * FROM "material" WHERE "id"=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.516139;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:30;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9133208;}i:54;a:6:{i:0;s:17:"Begin transaction";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:**********.520169;i:4;a:2:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9153720;}i:55;a:6:{i:0;s:2827:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status_group'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.521144;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:81;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9193776;}i:58;a:6:{i:0;s:889:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status_group'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.534139;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:81;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9204912;}i:61;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=7)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.550471;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:85;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9349960;}i:64;a:6:{i:0;s:174:"INSERT INTO "material_status_group" ("add_user_id", "status", "created_at", "accepted_user_id", "supplier_id") VALUES (7, 5, '2025-05-28 15:08:58', NULL, NULL) RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.552396;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:85;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9353472;}i:67;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.557657;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:97;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9391352;}i:70;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.567265;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:97;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9401560;}i:73;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.572493;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:102;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9422816;}i:76;a:6:{i:0;s:90:"SELECT EXISTS(SELECT * FROM "material_status_group" WHERE "material_status_group"."id"=47)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.574757;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:102;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9429432;}i:79;a:6:{i:0;s:147:"INSERT INTO "material_status" ("material_id", "quantity", "status_group_id", "created_at") VALUES (1, 20, 47, '2025-05-28 15:08:58') RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.580576;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:102;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9473960;}i:82;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.584759;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:111;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9502288;}i:85;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.593108;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:111;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9513088;}i:88;a:6:{i:0;s:135:"INSERT INTO "tracking" ("progress_type", "process_id", "created_at", "status") VALUES (20, 47, '2025-05-28 15:08:58', 0) RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.598213;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:116;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9527520;}i:91;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.602304;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:132;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9542656;}i:94;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.61112;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:132;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9553448;}i:97;a:6:{i:0;s:536:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (7, 'return_material_from_production', 'material_status_group', '"47"'::jsonb, '"{\"materials\":[{\"material_id\":1,\"quantity\":20}],\"description\":\"\\u0413\\u0440\\u0443\\u043f\\u043f\\u043e\\u0432\\u043e\\u0439 \\u0432\\u043e\\u0437\\u0432\\u0440\\u0430\\u0442 \\u043c\\u0430\\u0442\\u0435\\u0440\\u0438\\u0430\\u043b\\u043e\\u0432 \\u043d\\u0430 \\u0441\\u043a\\u043b\\u0430\\u0434\"}"'::jsonb, '2025-05-28 15:08:58')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.617193;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:132;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9570904;}i:100;a:6:{i:0;s:18:"Commit transaction";i:1;i:8;i:2;s:26:"yii\db\Transaction::commit";i:3;d:**********.620569;i:4;a:2:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:50;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9553952;}}}";s:9:"profiling";s:84483:"a:3:{s:6:"memory";i:9759872;s:4:"time";d:0.5706150531768799;s:8:"messages";a:48:{i:24;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.303118;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7485680;}i:25;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.405695;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7487984;}i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.409625;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8017528;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.449039;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8033264;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.451262;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8063352;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.455663;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8065288;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.46421;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8361560;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.469384;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8364320;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.479725;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8834728;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.484027;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8836896;}i:41;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.485243;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8841352;}i:42;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.487801;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8843456;}i:46;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.500605;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:30;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9117584;}i:47;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.508695;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:30;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9132096;}i:49;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.510011;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:30;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9128376;}i:50;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.515346;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:30;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9131584;}i:52;a:6:{i:0;s:37:"SELECT * FROM "material" WHERE "id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.516222;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:30;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9136568;}i:53;a:6:{i:0;s:37:"SELECT * FROM "material" WHERE "id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.518827;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:30;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9139464;}i:56;a:6:{i:0;s:2827:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status_group'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.521202;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:81;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9196016;}i:57;a:6:{i:0;s:2827:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status_group'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.532849;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:81;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9212016;}i:59;a:6:{i:0;s:889:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status_group'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.534211;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:81;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9207152;}i:60;a:6:{i:0;s:889:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status_group'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.53993;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:81;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9211544;}i:62;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=7)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.550557;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:85;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9353336;}i:63;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=7)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.551899;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:85;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9355320;}i:65;a:6:{i:0;s:174:"INSERT INTO "material_status_group" ("add_user_id", "status", "created_at", "accepted_user_id", "supplier_id") VALUES (7, 5, '2025-05-28 15:08:58', NULL, NULL) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.552414;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:85;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9360608;}i:66;a:6:{i:0;s:174:"INSERT INTO "material_status_group" ("add_user_id", "status", "created_at", "accepted_user_id", "supplier_id") VALUES (7, 5, '2025-05-28 15:08:58', NULL, NULL) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.555679;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:85;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9363128;}i:68;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.557728;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:97;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9393592;}i:69;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.566669;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:97;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9406600;}i:71;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.567302;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:97;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9403800;}i:72;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.571239;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:97;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9407600;}i:74;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.572534;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:102;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9426192;}i:75;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.573779;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:102;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9428176;}i:77;a:6:{i:0;s:90:"SELECT EXISTS(SELECT * FROM "material_status_group" WHERE "material_status_group"."id"=47)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.574804;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:102;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9432840;}i:78;a:6:{i:0;s:90:"SELECT EXISTS(SELECT * FROM "material_status_group" WHERE "material_status_group"."id"=47)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.577875;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:102;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9434856;}i:80;a:6:{i:0;s:147:"INSERT INTO "material_status" ("material_id", "quantity", "status_group_id", "created_at") VALUES (1, 20, 47, '2025-05-28 15:08:58') RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.580621;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:102;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9475464;}i:81;a:6:{i:0;s:147:"INSERT INTO "material_status" ("material_id", "quantity", "status_group_id", "created_at") VALUES (1, 20, 47, '2025-05-28 15:08:58') RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.58347;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:102;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9478008;}i:83;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.58481;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:111;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9504528;}i:84;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.591989;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:111;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9519080;}i:86;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.593159;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:111;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9515328;}i:87;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.597435;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:111;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9517640;}i:89;a:6:{i:0;s:135:"INSERT INTO "tracking" ("progress_type", "process_id", "created_at", "status") VALUES (20, 47, '2025-05-28 15:08:58', 0) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.598231;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:116;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9529024;}i:90;a:6:{i:0;s:135:"INSERT INTO "tracking" ("progress_type", "process_id", "created_at", "status") VALUES (20, 47, '2025-05-28 15:08:58', 0) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.600752;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:116;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9531568;}i:92;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.602396;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:132;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9544896;}i:93;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.609951;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:132;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9559376;}i:95;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.611195;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:132;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9555688;}i:96;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.615625;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:132;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9558872;}i:98;a:6:{i:0;s:536:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (7, 'return_material_from_production', 'material_status_group', '"47"'::jsonb, '"{\"materials\":[{\"material_id\":1,\"quantity\":20}],\"description\":\"\\u0413\\u0440\\u0443\\u043f\\u043f\\u043e\\u0432\\u043e\\u0439 \\u0432\\u043e\\u0437\\u0432\\u0440\\u0430\\u0442 \\u043c\\u0430\\u0442\\u0435\\u0440\\u0438\\u0430\\u043b\\u043e\\u0432 \\u043d\\u0430 \\u0441\\u043a\\u043b\\u0430\\u0434\"}"'::jsonb, '2025-05-28 15:08:58')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.617236;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:132;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9574528;}i:99;a:6:{i:0;s:536:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (7, 'return_material_from_production', 'material_status_group', '"47"'::jsonb, '"{\"materials\":[{\"material_id\":1,\"quantity\":20}],\"description\":\"\\u0413\\u0440\\u0443\\u043f\\u043f\\u043e\\u0432\\u043e\\u0439 \\u0432\\u043e\\u0437\\u0432\\u0440\\u0430\\u0442 \\u043c\\u0430\\u0442\\u0435\\u0440\\u0438\\u0430\\u043b\\u043e\\u0432 \\u043d\\u0430 \\u0441\\u043a\\u043b\\u0430\\u0434\"}"'::jsonb, '2025-05-28 15:08:58')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.620352;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:132;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9576664;}}}";s:2:"db";s:83238:"a:1:{s:8:"messages";a:46:{i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.409625;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8017528;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.449039;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8033264;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.451262;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8063352;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.455663;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8065288;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.46421;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8361560;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.469384;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8364320;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.479725;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8834728;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.484027;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8836896;}i:41;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.485243;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8841352;}i:42;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.487801;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8843456;}i:46;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.500605;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:30;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9117584;}i:47;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.508695;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:30;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9132096;}i:49;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.510011;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:30;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9128376;}i:50;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.515346;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:30;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9131584;}i:52;a:6:{i:0;s:37:"SELECT * FROM "material" WHERE "id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.516222;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:30;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9136568;}i:53;a:6:{i:0;s:37:"SELECT * FROM "material" WHERE "id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.518827;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:30;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9139464;}i:56;a:6:{i:0;s:2827:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status_group'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.521202;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:81;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9196016;}i:57;a:6:{i:0;s:2827:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status_group'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.532849;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:81;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9212016;}i:59;a:6:{i:0;s:889:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status_group'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.534211;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:81;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9207152;}i:60;a:6:{i:0;s:889:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status_group'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.53993;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:81;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9211544;}i:62;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=7)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.550557;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:85;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9353336;}i:63;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=7)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.551899;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:85;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9355320;}i:65;a:6:{i:0;s:174:"INSERT INTO "material_status_group" ("add_user_id", "status", "created_at", "accepted_user_id", "supplier_id") VALUES (7, 5, '2025-05-28 15:08:58', NULL, NULL) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.552414;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:85;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9360608;}i:66;a:6:{i:0;s:174:"INSERT INTO "material_status_group" ("add_user_id", "status", "created_at", "accepted_user_id", "supplier_id") VALUES (7, 5, '2025-05-28 15:08:58', NULL, NULL) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.555679;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:85;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9363128;}i:68;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.557728;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:97;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9393592;}i:69;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.566669;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:97;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9406600;}i:71;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.567302;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:97;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9403800;}i:72;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.571239;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:97;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9407600;}i:74;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.572534;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:102;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9426192;}i:75;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.573779;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:102;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9428176;}i:77;a:6:{i:0;s:90:"SELECT EXISTS(SELECT * FROM "material_status_group" WHERE "material_status_group"."id"=47)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.574804;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:102;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9432840;}i:78;a:6:{i:0;s:90:"SELECT EXISTS(SELECT * FROM "material_status_group" WHERE "material_status_group"."id"=47)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.577875;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:102;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9434856;}i:80;a:6:{i:0;s:147:"INSERT INTO "material_status" ("material_id", "quantity", "status_group_id", "created_at") VALUES (1, 20, 47, '2025-05-28 15:08:58') RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.580621;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:102;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9475464;}i:81;a:6:{i:0;s:147:"INSERT INTO "material_status" ("material_id", "quantity", "status_group_id", "created_at") VALUES (1, 20, 47, '2025-05-28 15:08:58') RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.58347;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:102;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9478008;}i:83;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.58481;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:111;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9504528;}i:84;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.591989;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:111;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9519080;}i:86;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.593159;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:111;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9515328;}i:87;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.597435;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:111;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9517640;}i:89;a:6:{i:0;s:135:"INSERT INTO "tracking" ("progress_type", "process_id", "created_at", "status") VALUES (20, 47, '2025-05-28 15:08:58', 0) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.598231;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:116;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9529024;}i:90;a:6:{i:0;s:135:"INSERT INTO "tracking" ("progress_type", "process_id", "created_at", "status") VALUES (20, 47, '2025-05-28 15:08:58', 0) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.600752;i:4;a:3:{i:0;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:116;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:571;s:8:"function";s:20:"sendToMaterialDefect";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9531568;}i:92;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.602396;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:132;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9544896;}i:93;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.609951;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:132;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9559376;}i:95;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.611195;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:132;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9555688;}i:96;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.615625;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:132;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9558872;}i:98;a:6:{i:0;s:536:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (7, 'return_material_from_production', 'material_status_group', '"47"'::jsonb, '"{\"materials\":[{\"material_id\":1,\"quantity\":20}],\"description\":\"\\u0413\\u0440\\u0443\\u043f\\u043f\\u043e\\u0432\\u043e\\u0439 \\u0432\\u043e\\u0437\\u0432\\u0440\\u0430\\u0442 \\u043c\\u0430\\u0442\\u0435\\u0440\\u0438\\u0430\\u043b\\u043e\\u0432 \\u043d\\u0430 \\u0441\\u043a\\u043b\\u0430\\u0434\"}"'::jsonb, '2025-05-28 15:08:58')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.617236;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:132;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9574528;}i:99;a:6:{i:0;s:536:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (7, 'return_material_from_production', 'material_status_group', '"47"'::jsonb, '"{\"materials\":[{\"material_id\":1,\"quantity\":20}],\"description\":\"\\u0413\\u0440\\u0443\\u043f\\u043f\\u043e\\u0432\\u043e\\u0439 \\u0432\\u043e\\u0437\\u0432\\u0440\\u0430\\u0442 \\u043c\\u0430\\u0442\\u0435\\u0440\\u0438\\u0430\\u043b\\u043e\\u0432 \\u043d\\u0430 \\u0441\\u043a\\u043b\\u0430\\u0434\"}"'::jsonb, '2025-05-28 15:08:58')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.620352;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:132;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:94:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\SendToMaterialDefectService.php";s:4:"line";i:42;s:8:"function";s:23:"returnMaterialToStorage";s:5:"class";s:63:"app\modules\api\services\manufacter\SendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9576664;}}}";s:5:"event";s:9640:"a:52:{i:0;a:5:{s:4:"time";d:**********.242592;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:**********.256537;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:**********.256566;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:3;a:5:{s:4:"time";d:**********.288081;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:4;a:5:{s:4:"time";d:**********.405678;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:5;a:5:{s:4:"time";d:**********.469669;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:6;a:5:{s:4:"time";d:**********.469739;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:7;a:5:{s:4:"time";d:**********.470087;s:4:"name";s:11:"beforeLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:8;a:5:{s:4:"time";d:**********.474075;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:**********.474143;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:**********.474171;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:**********.474193;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:**********.474212;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:**********.47423;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:**********.474247;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:**********.47457;s:4:"name";s:10:"afterLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:16;a:5:{s:4:"time";d:**********.474691;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:48:"app\modules\api\controllers\ManufacterController";}i:17;a:5:{s:4:"time";d:**********.499903;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"app\modules\api\models\SendToMaterialDefectForm";}i:18;a:5:{s:4:"time";d:**********.500463;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:19;a:5:{s:4:"time";d:**********.519111;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Material";}i:20;a:5:{s:4:"time";d:**********.519202;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Material";}i:21;a:5:{s:4:"time";d:**********.519304;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"app\modules\api\models\SendToMaterialDefectForm";}i:22;a:5:{s:4:"time";d:**********.520207;s:4:"name";s:16:"beginTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:23;a:5:{s:4:"time";d:**********.521032;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:24;a:5:{s:4:"time";d:**********.54081;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:25;a:5:{s:4:"time";d:**********.547927;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:26;a:5:{s:4:"time";d:**********.548206;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:27;a:5:{s:4:"time";d:**********.552199;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:28;a:5:{s:4:"time";d:**********.552226;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:29;a:5:{s:4:"time";d:**********.556857;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:30;a:5:{s:4:"time";d:**********.557523;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialStatus";}i:31;a:5:{s:4:"time";d:**********.572016;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialStatus";}i:32;a:5:{s:4:"time";d:**********.572256;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:33;a:5:{s:4:"time";d:**********.572375;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:34;a:5:{s:4:"time";d:**********.574306;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:35;a:5:{s:4:"time";d:**********.574589;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:36;a:5:{s:4:"time";d:**********.57843;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialStatus";}i:37;a:5:{s:4:"time";d:**********.578471;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialStatus";}i:38;a:5:{s:4:"time";d:**********.583972;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialStatus";}i:39;a:5:{s:4:"time";d:**********.584658;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:40;a:5:{s:4:"time";d:**********.597873;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:41;a:5:{s:4:"time";d:**********.598063;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:42;a:5:{s:4:"time";d:**********.598076;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:43;a:5:{s:4:"time";d:**********.601121;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:44;a:5:{s:4:"time";d:**********.624192;s:4:"name";s:17:"commitTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:45;a:5:{s:4:"time";d:**********.625421;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:48:"app\modules\api\controllers\ManufacterController";}i:46;a:5:{s:4:"time";d:**********.62693;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:47;a:5:{s:4:"time";d:**********.62696;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:48;a:5:{s:4:"time";d:**********.626986;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:49;a:5:{s:4:"time";d:**********.627006;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:50;a:5:{s:4:"time";d:**********.631293;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:51;a:5:{s:4:"time";d:**********.631471;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.068023;s:3:"end";d:**********.640055;s:6:"memory";i:9787632;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:2245:"a:3:{s:8:"messages";a:11:{i:9;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.244394;i:4;a:0:{}i:5;i:5773280;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.244528;i:4;a:0:{}i:5;i:5774032;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.244608;i:4;a:0:{}i:5;i:5774784;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.244635;i:4;a:0:{}i:5;i:5775536;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.244695;i:4;a:0:{}i:5;i:5776288;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.244721;i:4;a:0:{}i:5;i:5777040;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:5:"login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.244748;i:4;a:0:{}i:5;i:5777792;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:6:"logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.24477;i:4;a:0:{}i:5;i:5778544;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:13:"site/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.244804;i:4;a:0:{}i:5;i:5779936;}i:18;a:6:{i:0;s:69:"Request parsed with URL rule: api/<controller:[\w-]+>/<action:[\w-]+>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:**********.244884;i:4;a:0:{}i:5;i:5782032;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:39:"api/<controller:[\w-]+>/<action:[\w-]+>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.244893;i:4;a:0:{}i:5;i:5781848;}}s:5:"route";s:38:"api/manufacter/send-to-material-defect";s:6:"action";s:78:"app\modules\api\controllers\ManufacterController::actionSendToMaterialDefect()";}";s:7:"request";s:4750:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:11:{s:13:"authorization";s:47:"Bearer 681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy";s:12:"content-type";s:16:"application/json";s:10:"user-agent";s:21:"PostmanRuntime/7.44.0";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"1ddb3999-b53b-4757-b573-e4f3fe497ba0";s:4:"host";s:6:"silver";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:3:"232";s:6:"cookie";s:216:"PHPSESSID=tks4u3q28h6pbcdn0hd5bnnocqchaef3; _csrf=7c5a26d391a0150384285f00761cd605f31aef699d7c8db6030b6bd029605d7aa%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22El_nVZZNG15LS9GcZeGaYtb5Y_PGYD0Y%22%3B%7D";}s:15:"responseHeaders";a:9:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:4:"Vary";s:6:"Accept";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"6836e0ba35a2a";s:16:"X-Debug-Duration";s:3:"564";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=6836e0ba35a2a";s:10:"Set-Cookie";s:204:"_csrf=bdaae32f8314ef28a5bf31892da73508e4b3fe8c04fc3a80d84d98b17b513d1ba%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22WMX3wMQo41EUb-KM-cV587wukVNQX3us%22%3B%7D; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:38:"api/manufacter/send-to-material-defect";s:6:"action";s:78:"app\modules\api\controllers\ManufacterController::actionSendToMaterialDefect()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:16:"application/json";s:3:"Raw";s:232:"{
    "materials": [
        {
            "material_id": 1,
            "quantity": 20
        }
    ],
    "description": "Групповой возврат материалов на склад",
    "is_returned": true
}";s:7:"Decoded";a:3:{s:9:"materials";a:1:{i:0;a:2:{s:11:"material_id";i:1;s:8:"quantity";i:20;}}s:11:"description";s:70:"Групповой возврат материалов на склад";s:11:"is_returned";b:1;}}s:6:"SERVER";a:39:{s:15:"REDIRECT_STATUS";s:3:"200";s:12:"CONTENT_TYPE";s:16:"application/json";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.0";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"1ddb3999-b53b-4757-b573-e4f3fe497ba0";s:9:"HTTP_HOST";s:6:"silver";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:3:"232";s:11:"HTTP_COOKIE";s:216:"PHPSESSID=tks4u3q28h6pbcdn0hd5bnnocqchaef3; _csrf=7c5a26d391a0150384285f00761cd605f31aef699d7c8db6030b6bd029605d7aa%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22El_nVZZNG15LS9GcZeGaYtb5Y_PGYD0Y%22%3B%7D";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:6:"silver";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:44:"D:/OSPanel/domains/silverzavod/web/index.php";s:11:"REMOTE_PORT";s:5:"62995";s:12:"REDIRECT_URL";s:39:"/api/manufacter/send-to-material-defect";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:39:"/api/manufacter/send-to-material-defect";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.019725;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:2:{s:9:"PHPSESSID";s:32:"tks4u3q28h6pbcdn0hd5bnnocqchaef3";s:5:"_csrf";s:130:"7c5a26d391a0150384285f00761cd605f31aef699d7c8db6030b6bd029605d7aa:2:{i:0;s:5:"_csrf";i:1;s:32:"El_nVZZNG15LS9GcZeGaYtb5Y_PGYD0Y";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2147:"a:5:{s:2:"id";i:7;s:8:"identity";a:8:{s:2:"id";s:1:"7";s:8:"username";s:14:"'manufacturer'";s:9:"full_name";s:20:"'Ishlab chiqaruvchi'";s:4:"role";s:1:"0";s:12:"access_token";s:42:"'681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";s:8:"password";s:62:"'$2y$13$8ymQqJl5qbjAvbz9d5fbFuOFZGcDCeN6PAzjPCDDcPlYo7inyMDbG'";s:10:"created_at";s:21:"'2025-03-17 10:58:05'";s:10:"deleted_at";s:4:"null";}s:10:"attributes";a:8:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:10:"Логин";}i:2;a:2:{s:9:"attribute";s:9:"full_name";s:5:"label";s:9:"Full Name";}i:3;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:4;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:10:"Парол";}i:6;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:7;a:2:{s:9:"attribute";s:10:"deleted_at";s:5:"label";s:10:"Deleted At";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:1:{s:12:"manufacturer";a:7:{s:4:"type";i:1;s:4:"name";s:12:"manufacturer";s:11:"description";s:12:"Manufacturer";s:8:"ruleName";N;s:4:"data";s:4:"null";s:9:"createdAt";i:**********;s:9:"updatedAt";i:**********;}}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"6836e0ba35a2a";s:3:"url";s:52:"http://silver/api/manufacter/send-to-material-defect";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:**********.019725;s:10:"statusCode";i:200;s:8:"sqlCount";i:23;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9759872;s:14:"processingTime";d:0.5706150531768799;}s:10:"exceptions";a:0:{}}